﻿namespace CleverV2
{
    partial class Main
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            this.components = new System.ComponentModel.Container();
            System.ComponentModel.ComponentResourceManager resources = new System.ComponentModel.ComponentResourceManager(typeof(Main));
            this.fluentDesignFormContainer1 = new DevExpress.XtraBars.FluentDesignSystem.FluentDesignFormContainer();
            this.accordionControl1 = new DevExpress.XtraBars.Navigation.AccordionControl();
            this.Products = new DevExpress.XtraBars.Navigation.AccordionControlElement();
            this.ProductsList = new DevExpress.XtraBars.Navigation.AccordionControlElement();
            this.AddProducts = new DevExpress.XtraBars.Navigation.AccordionControlElement();
            this.AddUnit = new DevExpress.XtraBars.Navigation.AccordionControlElement();
            this.AddCategory = new DevExpress.XtraBars.Navigation.AccordionControlElement();
            this.Customer = new DevExpress.XtraBars.Navigation.AccordionControlElement();
            this.CustomerList = new DevExpress.XtraBars.Navigation.AccordionControlElement();
            this.CustomerMoney = new DevExpress.XtraBars.Navigation.AccordionControlElement();
            this.CustomerReport = new DevExpress.XtraBars.Navigation.AccordionControlElement();
            this.Suppliers = new DevExpress.XtraBars.Navigation.AccordionControlElement();
            this.SuppliersList = new DevExpress.XtraBars.Navigation.AccordionControlElement();
            this.SupplierMoney = new DevExpress.XtraBars.Navigation.AccordionControlElement();
            this.SupplierReport = new DevExpress.XtraBars.Navigation.AccordionControlElement();
            this.SalesGroup = new DevExpress.XtraBars.Navigation.AccordionControlElement();
            this.Sales = new DevExpress.XtraBars.Navigation.AccordionControlElement();
            this.SalesReport = new DevExpress.XtraBars.Navigation.AccordionControlElement();
            this.SalesProfits = new DevExpress.XtraBars.Navigation.AccordionControlElement();
            this.BuyGroup = new DevExpress.XtraBars.Navigation.AccordionControlElement();
            this.Buy = new DevExpress.XtraBars.Navigation.AccordionControlElement();
            this.BuyReport = new DevExpress.XtraBars.Navigation.AccordionControlElement();
            this.ReturnGroup = new DevExpress.XtraBars.Navigation.AccordionControlElement();
            this.Return = new DevExpress.XtraBars.Navigation.AccordionControlElement();
            this.ReturnReport = new DevExpress.XtraBars.Navigation.AccordionControlElement();
            this.DeservedGroup = new DevExpress.XtraBars.Navigation.AccordionControlElement();
            this.Deserved = new DevExpress.XtraBars.Navigation.AccordionControlElement();
            this.Stock = new DevExpress.XtraBars.Navigation.AccordionControlElement();
            this.StockAddMoney = new DevExpress.XtraBars.Navigation.AccordionControlElement();
            this.StockPullMoney = new DevExpress.XtraBars.Navigation.AccordionControlElement();
            this.CurrentMoney = new DevExpress.XtraBars.Navigation.AccordionControlElement();
            this.SettingGroup = new DevExpress.XtraBars.Navigation.AccordionControlElement();
            this.Setting = new DevExpress.XtraBars.Navigation.AccordionControlElement();
            this.UserPermission = new DevExpress.XtraBars.Navigation.AccordionControlElement();
            this.Backup = new DevExpress.XtraBars.Navigation.AccordionControlElement();
            this.Restore = new DevExpress.XtraBars.Navigation.AccordionControlElement();
            this.fluentDesignFormControl1 = new DevExpress.XtraBars.FluentDesignSystem.FluentDesignFormControl();
            this.fluentFormDefaultManager1 = new DevExpress.XtraBars.FluentDesignSystem.FluentFormDefaultManager(this.components);
            ((System.ComponentModel.ISupportInitialize)(this.accordionControl1)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.fluentDesignFormControl1)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.fluentFormDefaultManager1)).BeginInit();
            this.SuspendLayout();
            // 
            // fluentDesignFormContainer1
            // 
            this.fluentDesignFormContainer1.Dock = System.Windows.Forms.DockStyle.Fill;
            this.fluentDesignFormContainer1.Location = new System.Drawing.Point(439, 55);
            this.fluentDesignFormContainer1.Name = "fluentDesignFormContainer1";
            this.fluentDesignFormContainer1.Size = new System.Drawing.Size(1026, 873);
            this.fluentDesignFormContainer1.TabIndex = 0;
            // 
            // accordionControl1
            // 
            this.accordionControl1.Dock = System.Windows.Forms.DockStyle.Left;
            this.accordionControl1.Elements.AddRange(new DevExpress.XtraBars.Navigation.AccordionControlElement[] {
            this.Products,
            this.Customer,
            this.Suppliers,
            this.SalesGroup,
            this.BuyGroup,
            this.ReturnGroup,
            this.DeservedGroup,
            this.Stock,
            this.SettingGroup});
            this.accordionControl1.Location = new System.Drawing.Point(0, 55);
            this.accordionControl1.Name = "accordionControl1";
            this.accordionControl1.ScrollBarMode = DevExpress.XtraBars.Navigation.ScrollBarMode.Touch;
            this.accordionControl1.Size = new System.Drawing.Size(439, 873);
            this.accordionControl1.TabIndex = 1;
            this.accordionControl1.ViewType = DevExpress.XtraBars.Navigation.AccordionControlViewType.HamburgerMenu;
            // 
            // Products
            // 
            this.Products.Elements.AddRange(new DevExpress.XtraBars.Navigation.AccordionControlElement[] {
            this.ProductsList,
            this.AddProducts,
            this.AddUnit,
            this.AddCategory});
            this.Products.ImageOptions.SvgImage = ((DevExpress.Utils.Svg.SvgImage)(resources.GetObject("Products.ImageOptions.SvgImage")));
            this.Products.Name = "Products";
            this.Products.Text = "المنتجات";
            // 
            // ProductsList
            // 
            this.ProductsList.ImageOptions.SvgImage = ((DevExpress.Utils.Svg.SvgImage)(resources.GetObject("ProductsList.ImageOptions.SvgImage")));
            this.ProductsList.Name = "ProductsList";
            this.ProductsList.Style = DevExpress.XtraBars.Navigation.ElementStyle.Item;
            this.ProductsList.Text = "قائمة المنتجات";
            // 
            // AddProducts
            // 
            this.AddProducts.ImageOptions.SvgImage = ((DevExpress.Utils.Svg.SvgImage)(resources.GetObject("AddProducts.ImageOptions.SvgImage")));
            this.AddProducts.Name = "AddProducts";
            this.AddProducts.Style = DevExpress.XtraBars.Navigation.ElementStyle.Item;
            this.AddProducts.Text = "اضافة منتج";
            // 
            // AddUnit
            // 
            this.AddUnit.ImageOptions.SvgImage = ((DevExpress.Utils.Svg.SvgImage)(resources.GetObject("AddUnit.ImageOptions.SvgImage")));
            this.AddUnit.Name = "AddUnit";
            this.AddUnit.Style = DevExpress.XtraBars.Navigation.ElementStyle.Item;
            this.AddUnit.Text = "اضافة وحدة";
            // 
            // AddCategory
            // 
            this.AddCategory.ImageOptions.SvgImage = ((DevExpress.Utils.Svg.SvgImage)(resources.GetObject("AddCategory.ImageOptions.SvgImage")));
            this.AddCategory.Name = "AddCategory";
            this.AddCategory.Style = DevExpress.XtraBars.Navigation.ElementStyle.Item;
            this.AddCategory.Text = "اضافة صنف";
            // 
            // Customer
            // 
            this.Customer.Elements.AddRange(new DevExpress.XtraBars.Navigation.AccordionControlElement[] {
            this.CustomerList,
            this.CustomerMoney,
            this.CustomerReport});
            this.Customer.ImageOptions.SvgImage = ((DevExpress.Utils.Svg.SvgImage)(resources.GetObject("Customer.ImageOptions.SvgImage")));
            this.Customer.Name = "Customer";
            this.Customer.Text = "الزبائن";
            // 
            // CustomerList
            // 
            this.CustomerList.ImageOptions.SvgImage = ((DevExpress.Utils.Svg.SvgImage)(resources.GetObject("CustomerList.ImageOptions.SvgImage")));
            this.CustomerList.Name = "CustomerList";
            this.CustomerList.Style = DevExpress.XtraBars.Navigation.ElementStyle.Item;
            this.CustomerList.Text = "بيانات الزبائن";
            // 
            // CustomerMoney
            // 
            this.CustomerMoney.ImageOptions.SvgImage = ((DevExpress.Utils.Svg.SvgImage)(resources.GetObject("CustomerMoney.ImageOptions.SvgImage")));
            this.CustomerMoney.Name = "CustomerMoney";
            this.CustomerMoney.Style = DevExpress.XtraBars.Navigation.ElementStyle.Item;
            this.CustomerMoney.Text = "المبالغ المتبقية عليهم";
            // 
            // CustomerReport
            // 
            this.CustomerReport.ImageOptions.SvgImage = ((DevExpress.Utils.Svg.SvgImage)(resources.GetObject("CustomerReport.ImageOptions.SvgImage")));
            this.CustomerReport.Name = "CustomerReport";
            this.CustomerReport.Style = DevExpress.XtraBars.Navigation.ElementStyle.Item;
            this.CustomerReport.Text = "المبالغ المدفوعة منهم";
            // 
            // Suppliers
            // 
            this.Suppliers.Elements.AddRange(new DevExpress.XtraBars.Navigation.AccordionControlElement[] {
            this.SuppliersList,
            this.SupplierMoney,
            this.SupplierReport});
            this.Suppliers.ImageOptions.SvgImage = ((DevExpress.Utils.Svg.SvgImage)(resources.GetObject("Suppliers.ImageOptions.SvgImage")));
            this.Suppliers.Name = "Suppliers";
            this.Suppliers.Text = "الموردين";
            // 
            // SuppliersList
            // 
            this.SuppliersList.ImageOptions.SvgImage = ((DevExpress.Utils.Svg.SvgImage)(resources.GetObject("SuppliersList.ImageOptions.SvgImage")));
            this.SuppliersList.Name = "SuppliersList";
            this.SuppliersList.Style = DevExpress.XtraBars.Navigation.ElementStyle.Item;
            this.SuppliersList.Text = "بيانات الموردين";
            // 
            // SupplierMoney
            // 
            this.SupplierMoney.ImageOptions.SvgImage = ((DevExpress.Utils.Svg.SvgImage)(resources.GetObject("SupplierMoney.ImageOptions.SvgImage")));
            this.SupplierMoney.Name = "SupplierMoney";
            this.SupplierMoney.Style = DevExpress.XtraBars.Navigation.ElementStyle.Item;
            this.SupplierMoney.Text = "المبالغ المتبقية لهم";
            // 
            // SupplierReport
            // 
            this.SupplierReport.ImageOptions.SvgImage = ((DevExpress.Utils.Svg.SvgImage)(resources.GetObject("SupplierReport.ImageOptions.SvgImage")));
            this.SupplierReport.Name = "SupplierReport";
            this.SupplierReport.Style = DevExpress.XtraBars.Navigation.ElementStyle.Item;
            this.SupplierReport.Text = "المبالغ المدفوعة لهم";
            // 
            // SalesGroup
            // 
            this.SalesGroup.Elements.AddRange(new DevExpress.XtraBars.Navigation.AccordionControlElement[] {
            this.Sales,
            this.SalesReport,
            this.SalesProfits});
            this.SalesGroup.ImageOptions.SvgImage = ((DevExpress.Utils.Svg.SvgImage)(resources.GetObject("SalesGroup.ImageOptions.SvgImage")));
            this.SalesGroup.Name = "SalesGroup";
            this.SalesGroup.Text = "المبيعات";
            // 
            // Sales
            // 
            this.Sales.ImageOptions.SvgImage = ((DevExpress.Utils.Svg.SvgImage)(resources.GetObject("Sales.ImageOptions.SvgImage")));
            this.Sales.Name = "Sales";
            this.Sales.Style = DevExpress.XtraBars.Navigation.ElementStyle.Item;
            this.Sales.Text = "ادارة المبيعات";
            // 
            // SalesReport
            // 
            this.SalesReport.ImageOptions.SvgImage = ((DevExpress.Utils.Svg.SvgImage)(resources.GetObject("SalesReport.ImageOptions.SvgImage")));
            this.SalesReport.Name = "SalesReport";
            this.SalesReport.Style = DevExpress.XtraBars.Navigation.ElementStyle.Item;
            this.SalesReport.Text = "تقارير المبيعات";
            // 
            // SalesProfits
            // 
            this.SalesProfits.ImageOptions.SvgImage = ((DevExpress.Utils.Svg.SvgImage)(resources.GetObject("SalesProfits.ImageOptions.SvgImage")));
            this.SalesProfits.Name = "SalesProfits";
            this.SalesProfits.Style = DevExpress.XtraBars.Navigation.ElementStyle.Item;
            this.SalesProfits.Text = "ارباح المبيعات";
            // 
            // BuyGroup
            // 
            this.BuyGroup.Elements.AddRange(new DevExpress.XtraBars.Navigation.AccordionControlElement[] {
            this.Buy,
            this.BuyReport});
            this.BuyGroup.ImageOptions.SvgImage = ((DevExpress.Utils.Svg.SvgImage)(resources.GetObject("BuyGroup.ImageOptions.SvgImage")));
            this.BuyGroup.Name = "BuyGroup";
            this.BuyGroup.Text = "المشتريات";
            // 
            // Buy
            // 
            this.Buy.ImageOptions.SvgImage = ((DevExpress.Utils.Svg.SvgImage)(resources.GetObject("Buy.ImageOptions.SvgImage")));
            this.Buy.Name = "Buy";
            this.Buy.Style = DevExpress.XtraBars.Navigation.ElementStyle.Item;
            this.Buy.Text = "ادارة المشتريات";
            // 
            // BuyReport
            // 
            this.BuyReport.ImageOptions.SvgImage = ((DevExpress.Utils.Svg.SvgImage)(resources.GetObject("BuyReport.ImageOptions.SvgImage")));
            this.BuyReport.Name = "BuyReport";
            this.BuyReport.Style = DevExpress.XtraBars.Navigation.ElementStyle.Item;
            this.BuyReport.Text = "تقارير المشتريات";
            // 
            // ReturnGroup
            // 
            this.ReturnGroup.Elements.AddRange(new DevExpress.XtraBars.Navigation.AccordionControlElement[] {
            this.Return,
            this.ReturnReport});
            this.ReturnGroup.ImageOptions.SvgImage = ((DevExpress.Utils.Svg.SvgImage)(resources.GetObject("ReturnGroup.ImageOptions.SvgImage")));
            this.ReturnGroup.Name = "ReturnGroup";
            this.ReturnGroup.Text = "المرتجعات";
            // 
            // Return
            // 
            this.Return.ImageOptions.SvgImage = ((DevExpress.Utils.Svg.SvgImage)(resources.GetObject("Return.ImageOptions.SvgImage")));
            this.Return.Name = "Return";
            this.Return.Style = DevExpress.XtraBars.Navigation.ElementStyle.Item;
            this.Return.Text = "ادارت المرتجعات";
            // 
            // ReturnReport
            // 
            this.ReturnReport.ImageOptions.SvgImage = ((DevExpress.Utils.Svg.SvgImage)(resources.GetObject("ReturnReport.ImageOptions.SvgImage")));
            this.ReturnReport.Name = "ReturnReport";
            this.ReturnReport.Style = DevExpress.XtraBars.Navigation.ElementStyle.Item;
            this.ReturnReport.Text = "تقارير المرتجعات";
            // 
            // DeservedGroup
            // 
            this.DeservedGroup.Elements.AddRange(new DevExpress.XtraBars.Navigation.AccordionControlElement[] {
            this.Deserved});
            this.DeservedGroup.ImageOptions.SvgImage = ((DevExpress.Utils.Svg.SvgImage)(resources.GetObject("DeservedGroup.ImageOptions.SvgImage")));
            this.DeservedGroup.Name = "DeservedGroup";
            this.DeservedGroup.Text = "المصاريف";
            // 
            // Deserved
            // 
            this.Deserved.ImageOptions.SvgImage = ((DevExpress.Utils.Svg.SvgImage)(resources.GetObject("Deserved.ImageOptions.SvgImage")));
            this.Deserved.Name = "Deserved";
            this.Deserved.Style = DevExpress.XtraBars.Navigation.ElementStyle.Item;
            this.Deserved.Text = "المصاريف";
            this.Deserved.Click += new System.EventHandler(this.Deserved_Click);
            // 
            // Stock
            // 
            this.Stock.Elements.AddRange(new DevExpress.XtraBars.Navigation.AccordionControlElement[] {
            this.StockAddMoney,
            this.StockPullMoney,
            this.CurrentMoney});
            this.Stock.ImageOptions.SvgImage = ((DevExpress.Utils.Svg.SvgImage)(resources.GetObject("Stock.ImageOptions.SvgImage")));
            this.Stock.Name = "Stock";
            this.Stock.Text = "الخزنة";
            // 
            // StockAddMoney
            // 
            this.StockAddMoney.ImageOptions.SvgImage = ((DevExpress.Utils.Svg.SvgImage)(resources.GetObject("StockAddMoney.ImageOptions.SvgImage")));
            this.StockAddMoney.Name = "StockAddMoney";
            this.StockAddMoney.Style = DevExpress.XtraBars.Navigation.ElementStyle.Item;
            this.StockAddMoney.Text = "ايداع في الخزنة";
            // 
            // StockPullMoney
            // 
            this.StockPullMoney.ImageOptions.SvgImage = ((DevExpress.Utils.Svg.SvgImage)(resources.GetObject("StockPullMoney.ImageOptions.SvgImage")));
            this.StockPullMoney.Name = "StockPullMoney";
            this.StockPullMoney.Style = DevExpress.XtraBars.Navigation.ElementStyle.Item;
            this.StockPullMoney.Text = "سحب من الخزنة";
            // 
            // CurrentMoney
            // 
            this.CurrentMoney.ImageOptions.SvgImage = ((DevExpress.Utils.Svg.SvgImage)(resources.GetObject("CurrentMoney.ImageOptions.SvgImage")));
            this.CurrentMoney.Name = "CurrentMoney";
            this.CurrentMoney.Style = DevExpress.XtraBars.Navigation.ElementStyle.Item;
            this.CurrentMoney.Text = "الرصيد الحالي للخزنة";
            // 
            // SettingGroup
            // 
            this.SettingGroup.Elements.AddRange(new DevExpress.XtraBars.Navigation.AccordionControlElement[] {
            this.Setting,
            this.UserPermission,
            this.Backup,
            this.Restore});
            this.SettingGroup.ImageOptions.SvgImage = ((DevExpress.Utils.Svg.SvgImage)(resources.GetObject("SettingGroup.ImageOptions.SvgImage")));
            this.SettingGroup.Name = "SettingGroup";
            this.SettingGroup.Text = "الاعدادات";
            // 
            // Setting
            // 
            this.Setting.ImageOptions.SvgImage = ((DevExpress.Utils.Svg.SvgImage)(resources.GetObject("Setting.ImageOptions.SvgImage")));
            this.Setting.Name = "Setting";
            this.Setting.Style = DevExpress.XtraBars.Navigation.ElementStyle.Item;
            this.Setting.Text = "اعدادات البرنامج";
            // 
            // UserPermission
            // 
            this.UserPermission.ImageOptions.SvgImage = ((DevExpress.Utils.Svg.SvgImage)(resources.GetObject("UserPermission.ImageOptions.SvgImage")));
            this.UserPermission.Name = "UserPermission";
            this.UserPermission.Style = DevExpress.XtraBars.Navigation.ElementStyle.Item;
            this.UserPermission.Text = "صلاحيات المستخدمين";
            // 
            // Backup
            // 
            this.Backup.ImageOptions.SvgImage = ((DevExpress.Utils.Svg.SvgImage)(resources.GetObject("Backup.ImageOptions.SvgImage")));
            this.Backup.Name = "Backup";
            this.Backup.Style = DevExpress.XtraBars.Navigation.ElementStyle.Item;
            this.Backup.Text = "اخذ نسخة احتياطية";
            // 
            // Restore
            // 
            this.Restore.ImageOptions.SvgImage = ((DevExpress.Utils.Svg.SvgImage)(resources.GetObject("Restore.ImageOptions.SvgImage")));
            this.Restore.Name = "Restore";
            this.Restore.Style = DevExpress.XtraBars.Navigation.ElementStyle.Item;
            this.Restore.Text = "استعادة نسخة مأخوذة";
            // 
            // fluentDesignFormControl1
            // 
            this.fluentDesignFormControl1.FluentDesignForm = this;
            this.fluentDesignFormControl1.Location = new System.Drawing.Point(0, 0);
            this.fluentDesignFormControl1.Manager = this.fluentFormDefaultManager1;
            this.fluentDesignFormControl1.Name = "fluentDesignFormControl1";
            this.fluentDesignFormControl1.Size = new System.Drawing.Size(1465, 55);
            this.fluentDesignFormControl1.TabIndex = 2;
            this.fluentDesignFormControl1.TabStop = false;
            // 
            // fluentFormDefaultManager1
            // 
            this.fluentFormDefaultManager1.Form = this;
            // 
            // Main
            // 
            this.Appearance.Options.UseFont = true;
            this.AutoScaleDimensions = new System.Drawing.SizeF(168F, 168F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Dpi;
            this.AutoSize = true;
            this.ClientSize = new System.Drawing.Size(1465, 928);
            this.ControlContainer = this.fluentDesignFormContainer1;
            this.Controls.Add(this.fluentDesignFormContainer1);
            this.Controls.Add(this.accordionControl1);
            this.Controls.Add(this.fluentDesignFormControl1);
            this.FluentDesignFormControl = this.fluentDesignFormControl1;
            this.Font = new System.Drawing.Font("Tahoma", 11.14286F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.FormBorderStyle = System.Windows.Forms.FormBorderStyle.FixedDialog;
            this.MaximizeBox = false;
            this.Name = "Main";
            this.NavigationControl = this.accordionControl1;
            this.RightToLeft = System.Windows.Forms.RightToLeft.Yes;
            this.RightToLeftLayout = true;
            this.StartPosition = System.Windows.Forms.FormStartPosition.CenterScreen;
            this.Text = "Clever";
            this.WindowState = System.Windows.Forms.FormWindowState.Maximized;
            ((System.ComponentModel.ISupportInitialize)(this.accordionControl1)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.fluentDesignFormControl1)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.fluentFormDefaultManager1)).EndInit();
            this.ResumeLayout(false);

        }

        #endregion
        private DevExpress.XtraBars.FluentDesignSystem.FluentDesignFormContainer fluentDesignFormContainer1;
        private DevExpress.XtraBars.Navigation.AccordionControl accordionControl1;
        private DevExpress.XtraBars.FluentDesignSystem.FluentDesignFormControl fluentDesignFormControl1;
        private DevExpress.XtraBars.Navigation.AccordionControlElement Products;
        private DevExpress.XtraBars.FluentDesignSystem.FluentFormDefaultManager fluentFormDefaultManager1;
        private DevExpress.XtraBars.Navigation.AccordionControlElement AddProducts;
        private DevExpress.XtraBars.Navigation.AccordionControlElement ProductsList;
        private DevExpress.XtraBars.Navigation.AccordionControlElement AddUnit;
        private DevExpress.XtraBars.Navigation.AccordionControlElement AddCategory;
        private DevExpress.XtraBars.Navigation.AccordionControlElement Customer;
        private DevExpress.XtraBars.Navigation.AccordionControlElement CustomerList;
        private DevExpress.XtraBars.Navigation.AccordionControlElement CustomerMoney;
        private DevExpress.XtraBars.Navigation.AccordionControlElement CustomerReport;
        private DevExpress.XtraBars.Navigation.AccordionControlElement Suppliers;
        private DevExpress.XtraBars.Navigation.AccordionControlElement SuppliersList;
        private DevExpress.XtraBars.Navigation.AccordionControlElement SupplierMoney;
        private DevExpress.XtraBars.Navigation.AccordionControlElement SupplierReport;
        private DevExpress.XtraBars.Navigation.AccordionControlElement SalesGroup;
        private DevExpress.XtraBars.Navigation.AccordionControlElement BuyGroup;
        private DevExpress.XtraBars.Navigation.AccordionControlElement ReturnGroup;
        private DevExpress.XtraBars.Navigation.AccordionControlElement DeservedGroup;
        private DevExpress.XtraBars.Navigation.AccordionControlElement Stock;
        private DevExpress.XtraBars.Navigation.AccordionControlElement SettingGroup;
        private DevExpress.XtraBars.Navigation.AccordionControlElement Sales;
        private DevExpress.XtraBars.Navigation.AccordionControlElement SalesReport;
        private DevExpress.XtraBars.Navigation.AccordionControlElement SalesProfits;
        private DevExpress.XtraBars.Navigation.AccordionControlElement Buy;
        private DevExpress.XtraBars.Navigation.AccordionControlElement BuyReport;
        private DevExpress.XtraBars.Navigation.AccordionControlElement Return;
        private DevExpress.XtraBars.Navigation.AccordionControlElement ReturnReport;
        private DevExpress.XtraBars.Navigation.AccordionControlElement Deserved;
        private DevExpress.XtraBars.Navigation.AccordionControlElement StockAddMoney;
        private DevExpress.XtraBars.Navigation.AccordionControlElement StockPullMoney;
        private DevExpress.XtraBars.Navigation.AccordionControlElement CurrentMoney;
        private DevExpress.XtraBars.Navigation.AccordionControlElement Setting;
        private DevExpress.XtraBars.Navigation.AccordionControlElement UserPermission;
        private DevExpress.XtraBars.Navigation.AccordionControlElement Backup;
        private DevExpress.XtraBars.Navigation.AccordionControlElement Restore;
    }
}

