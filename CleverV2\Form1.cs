﻿using DevExpress.XtraBars;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Windows.Forms;

namespace CleverV2
{
    public partial class Main : DevExpress.XtraBars.FluentDesignSystem.FluentDesignForm
    {
        private UC_Deserved uC_Deserved;
        public Main()
        {
            InitializeComponent();
        }

        private void Deserved_Click(object sender, EventArgs e)
        {
            // تنظيف fluentDesignFormContainer1 من أي محتوى سابق
            fluentDesignFormContainer1.Controls.Clear();

            // إنشاء UserControl جديد في كل مرة
            uC_Deserved = new UC_Deserved();
            uC_Deserved.Dock = DockStyle.Fill;

            // إضافة UserControl إلى fluentDesignFormContainer1
            fluentDesignFormContainer1.Controls.Add(uC_Deserved);

            // إظهار fluentDesignFormContainer1 إذا كان مخفي
            fluentDesignFormContainer1.Visible = true;
        }
    }
}
